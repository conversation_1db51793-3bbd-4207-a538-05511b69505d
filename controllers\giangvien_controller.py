from config import get_db_connection

def get_all():
    """
    L<PERSON>y danh sách tất cả giảng viên từ database

    Returns:
        list: <PERSON><PERSON> sách các tuple chứa thông tin giảng viên
              Mỗi tuple có format: (MaGV, TenGV)

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # Tạo câu lệnh SQL để lấy tất cả giảng viên
            sql = "SELECT MaGV, TenGV FROM GiangVien ORDER BY MaGV"
            cursor.execute(sql)

            # Chuyển đổi kết quả thành list of dictionaries để dễ sử dụng
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results
    except Exception as e:
        raise Exception(f"Lỗi khi lấy danh sách giảng viên: {str(e)}")

def create_new(data):
    """
    Tạo giảng viên mới trong database

    Args:
        data (dict): Dictionary chứa thông tin giảng viên
                    Bắt buộc có: MaGV, TenGV

    Returns:
        dict: Thông tin giảng viên vừa được tạo

    Raises:
        Exception: Khi có lỗi kết nối database, thực thi SQL, hoặc dữ liệu không hợp lệ
    """
    try:
        # Kiểm tra dữ liệu đầu vào
        if not data.get("MaGV") or not data.get("TenGV"):
            raise ValueError("MaGV và TenGV là bắt buộc")

        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem MaGV đã tồn tại chưa
            check_sql = "SELECT COUNT(*) FROM GiangVien WHERE MaGV = ?"
            cursor.execute(check_sql, (data["MaGV"],))
            if cursor.fetchone()[0] > 0:
                raise ValueError(f"Mã giảng viên {data['MaGV']} đã tồn tại")

            # Thêm giảng viên mới
            sql = "INSERT INTO GiangVien(MaGV, TenGV) VALUES(?, ?)"
            cursor.execute(sql, (data["MaGV"], data["TenGV"]))
            conn.commit()

            return {
                "MaGV": data["MaGV"],
                "TenGV": data["TenGV"]
            }
    except ValueError as ve:
        raise Exception(str(ve))
    except Exception as e:
        raise Exception(f"Lỗi khi tạo giảng viên: {str(e)}")

def update_information(MaGV, data):
    """
    Cập nhật thông tin giảng viên

    Args:
        MaGV (str): Mã giảng viên cần cập nhật
        data (dict): Dictionary chứa thông tin cần cập nhật

    Returns:
        dict: Thông tin giảng viên sau khi cập nhật, hoặc None nếu không tìm thấy

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem giảng viên có tồn tại không
            check_sql = "SELECT COUNT(*) FROM GiangVien WHERE MaGV = ?"
            cursor.execute(check_sql, (MaGV,))
            if cursor.fetchone()[0] == 0:
                return None  # Không tìm thấy giảng viên

            # Cập nhật thông tin giảng viên
            sql = "UPDATE GiangVien SET TenGV = ? WHERE MaGV = ?"
            cursor.execute(sql, (data["TenGV"], MaGV))
            conn.commit()

            # Trả về thông tin đã cập nhật
            return {
                "MaGV": MaGV,
                "TenGV": data["TenGV"]
            }
    except Exception as e:
        raise Exception(f"Lỗi khi cập nhật giảng viên: {str(e)}")

def delete_GV(MaGV):
    """
    Xóa giảng viên khỏi database

    Args:
        MaGV (str): Mã giảng viên cần xóa

    Returns:
        bool: True nếu xóa thành công, False nếu không tìm thấy giảng viên

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem giảng viên có tồn tại không
            check_sql = "SELECT COUNT(*) FROM GiangVien WHERE MaGV = ?"
            cursor.execute(check_sql, (MaGV,))
            if cursor.fetchone()[0] == 0:
                return False  # Không tìm thấy giảng viên

            # Xóa giảng viên
            sql = "DELETE FROM GiangVien WHERE MaGV = ?"
            cursor.execute(sql, (MaGV,))
            conn.commit()

            return True
    except Exception as e:
        raise Exception(f"Lỗi khi xóa giảng viên: {str(e)}")

def search_MaGV(MaGV):
    """
    Tìm kiếm giảng viên theo mã (hỗ trợ tìm kiếm một phần)

    Args:
        MaGV (str): Mã giảng viên cần tìm (có thể là một phần)

    Returns:
        list: Danh sách các dictionary chứa thông tin giảng viên phù hợp

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Sử dụng parameterized query để tránh SQL injection
            # FIXED: Sửa lỗi SQL injection bằng cách sử dụng parameter thay vì string formatting
            sql = "SELECT MaGV, TenGV FROM GiangVien WHERE MaGV LIKE ? ORDER BY MaGV"
            search_pattern = f"%{MaGV}%"  # Tạo pattern tìm kiếm
            cursor.execute(sql, (search_pattern,))

            # Chuyển đổi kết quả thành list of dictionaries
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results
    except Exception as e:
        raise Exception(f"Lỗi khi tìm kiếm giảng viên: {str(e)}")