from config import get_db_connection

def get_all(): 
    with get_db_connection() as conn: 
        cursor = conn.cursor()
        # tạo câu lệnh SQL 
        sql = "SELECT * FROM GiangVien"
        cursor.execute(sql)
        return cursor.fetchall()

def create_new(data): 
    with get_db_connection() as conn: 
        cursor = conn.cursor()
        sql = "INSERT INTO GiangVien(MaGV, TenGV) VALUES(?,?)"
        cursor.execute(sql,(data["MaGV"], data["TenGV"]))
        conn.commit()
        return data
def update_information(MaGV, data): 
    with get_db_connection() as conn: 
        cursor = conn.cursor()
        sql = "UPDATE GiangVien SET TenGV = ? WHERE MaGV = ?"
        cursor.execute(sql, (data["TenGV"], MaGV))
        conn.commit()
        return data 
     
def delete_GV(MaGV): 
    with get_db_connection() as conn: 
        cursor = conn.cursor()
        sql = "DELETE FROM GiangVien WHERE MaGV = ?"
        cursor.execute(sql, (MaGV,))
        conn.commit()
        return True
     
def search_MaGV(MaGV): 
    with get_db_connection() as conn: 
        cursor = conn.cursor()
        sql = "SELECT * FROM GiangVien WHERE MAGV LIKE "%{MaGV}%" "  
        cursor.execute(sql) 
        return cursor.fetchall()

        
        
        