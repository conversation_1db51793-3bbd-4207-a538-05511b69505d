<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản Lý Sinh Viên - Trườ<PERSON> H<PERSON></title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">

</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎓 Hệ Thống Quản Lý Sinh Viên</h1>
            <p>Quản lý thông tin sinh viên một cách dễ dàng và hiệu quả</p>
        </div>

        <!-- Alert Messages -->
        <div id="alertContainer"></div>

        <!-- Add Student Form -->
        <div class="section">
            <h2>➕ Thêm Sinh Viên Mớ<PERSON></h2>
            <form id="addStudentForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="maSV">Mã Sinh Viên *</label>
                        <input type="text" id="maSV" name="maSV" required placeholder="VD: SV001">
                    </div>
                    <div class="form-group">
                        <label for="tenSV">Tên Sinh Viên *</label>
                        <input type="text" id="tenSV" name="tenSV" required placeholder="VD: Nguyễn Văn A">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required placeholder="VD: <EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="soDT">Số Điện Thoại *</label>
                        <input type="text" id="soDT" name="soDT" required placeholder="VD: 0123456789">
                    </div>
                </div>
                <div class="form-group">
                    <label for="lop">Lớp</label>
                    <input type="text" id="lop" name="lop" placeholder="VD: CNTT01">
                </div>
                <button type="submit" class="btn">Thêm Sinh Viên</button>
                <button type="reset" class="btn btn-secondary">Xóa Form</button>
            </form>
        </div>

        <!-- Search Students -->
        <div class="section">
            <h2>🔍 Tìm Kiếm Sinh Viên</h2>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Nhập mã sinh viên để tìm kiếm...">
                <button onclick="searchStudents()" class="btn">Tìm Kiếm</button>
                <button onclick="loadAllStudents()" class="btn btn-secondary">Hiển Thị Tất Cả</button>
            </div>
        </div>

        <!-- Student List -->
        <div class="section">
            <h2>📋 Danh Sách Sinh Viên</h2>
            <div id="studentList">
                <div class="loading">Đang tải danh sách sinh viên...</div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='app.js') }}"></script>



</body>

</html>