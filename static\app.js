// Quản lý sinh viên - JavaScript Functions
// API Base URL
const API_BASE = '/api';

// Current editing student
let editingStudent = null;

// Load all students when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAllStudents();
    initializeEventListeners();
});

// Initialize event listeners
function initializeEventListeners() {
    // Add Student Form Handler
    document.getElementById('addStudentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (editingStudent) {
            updateStudent();
        } else {
            addStudent();
        }
    });

    // Reset form when reset button is clicked
    document.querySelector('#addStudentForm button[type="reset"]').addEventListener('click', function() {
        resetForm();
    });

    // Search on Enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchStudents();
        }
    });
}

// Show alert message
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    alertContainer.innerHTML = '';
    alertContainer.appendChild(alertDiv);
    
    // Auto hide after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Add new student
async function addStudent() {
    const formData = new FormData(document.getElementById('addStudentForm'));
    const studentData = {
        MaSV: formData.get('maSV'),
        TenSV: formData.get('tenSV'),
        Email: formData.get('email'),
        SoDT: formData.get('soDT'),
        Lop: formData.get('lop') || null
    };

    // Validate required fields
    if (!studentData.MaSV || !studentData.TenSV || !studentData.Email || !studentData.SoDT) {
        showAlert('Vui lòng điền đầy đủ các trường bắt buộc!', 'error');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(studentData.Email)) {
        showAlert('Định dạng email không hợp lệ!', 'error');
        return;
    }

    // Validate phone number (basic validation)
    const phoneRegex = /^[0-9]{10,11}$/;
    if (!phoneRegex.test(studentData.SoDT)) {
        showAlert('Số điện thoại phải có 10-11 chữ số!', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/SinhVien`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(studentData)
        });

        if (response.ok) {
            const newStudent = await response.json();
            showAlert('✅ Thêm sinh viên thành công!', 'success');
            document.getElementById('addStudentForm').reset();
            loadAllStudents();
        } else {
            const error = await response.json();
            showAlert(`❌ Lỗi: ${error.message || 'Không thể thêm sinh viên'}`, 'error');
        }
    } catch (error) {
        showAlert(`❌ Lỗi kết nối: ${error.message}`, 'error');
    }
}

// Load all students
async function loadAllStudents() {
    const studentList = document.getElementById('studentList');
    studentList.innerHTML = '<div class="loading">Đang tải danh sách sinh viên...</div>';

    try {
        const response = await fetch(`${API_BASE}/SinhVien`);
        const students = await response.json();

        if (students.length === 0) {
            studentList.innerHTML = `
                <div class="empty-state">
                    <div style="font-size: 3em; margin-bottom: 15px;">📚</div>
                    <h3>Chưa có sinh viên nào</h3>
                    <p>Hãy thêm sinh viên đầu tiên bằng form ở trên</p>
                </div>
            `;
        } else {
            displayStudents(students);
        }
    } catch (error) {
        studentList.innerHTML = `
            <div class="alert alert-error">
                ❌ Lỗi khi tải danh sách sinh viên: ${error.message}
            </div>
        `;
    }
}

// Search students
async function searchStudents() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    
    if (!searchTerm) {
        showAlert('Vui lòng nhập mã sinh viên để tìm kiếm', 'error');
        return;
    }

    const studentList = document.getElementById('studentList');
    studentList.innerHTML = '<div class="loading">Đang tìm kiếm...</div>';

    try {
        const response = await fetch(`${API_BASE}/SinhVien/Search?MaSV=${encodeURIComponent(searchTerm)}`);
        const students = await response.json();

        if (students.length === 0) {
            studentList.innerHTML = `
                <div class="empty-state">
                    <div style="font-size: 3em; margin-bottom: 15px;">🔍</div>
                    <h3>Không tìm thấy sinh viên</h3>
                    <p>Không có sinh viên nào có mã chứa "${searchTerm}"</p>
                </div>
            `;
        } else {
            displayStudents(students);
            showAlert(`🔍 Tìm thấy ${students.length} sinh viên`, 'success');
        }
    } catch (error) {
        studentList.innerHTML = `
            <div class="alert alert-error">
                ❌ Lỗi khi tìm kiếm: ${error.message}
            </div>
        `;
    }
}

// Display students
function displayStudents(students) {
    const studentList = document.getElementById('studentList');
    
    const studentsHTML = students.map(student => `
        <div class="student-card">
            <div class="student-info">
                <div class="info-item">
                    <span class="info-label">Mã SV</span>
                    <span class="info-value">${student.MaSV}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tên sinh viên</span>
                    <span class="info-value">${student.TenSV}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email</span>
                    <span class="info-value">${student.Email}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Số điện thoại</span>
                    <span class="info-value">${student.SoDT}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Lớp</span>
                    <span class="info-value">${student.Lop || 'Chưa có'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Xếp loại</span>
                    <span class="info-value">${student.MaXL || 'Chưa có'}</span>
                </div>
            </div>
            <div class="student-actions">
                <button onclick="editStudent('${student.MaSV}')" class="btn btn-secondary">✏️ Sửa</button>
                <button onclick="deleteStudent('${student.MaSV}')" class="btn btn-danger">🗑️ Xóa</button>
            </div>
        </div>
    `).join('');

    studentList.innerHTML = studentsHTML;
}

// Edit student
function editStudent(maSV) {
    // Find student data
    const studentCards = document.querySelectorAll('.student-card');
    let studentData = null;
    
    studentCards.forEach(card => {
        const maSVElement = card.querySelector('.info-value');
        if (maSVElement && maSVElement.textContent === maSV) {
            const infoValues = card.querySelectorAll('.info-value');
            studentData = {
                MaSV: infoValues[0].textContent,
                TenSV: infoValues[1].textContent,
                Email: infoValues[2].textContent,
                SoDT: infoValues[3].textContent,
                Lop: infoValues[4].textContent === 'Chưa có' ? '' : infoValues[4].textContent
            };
        }
    });

    if (studentData) {
        // Fill form with student data
        document.getElementById('maSV').value = studentData.MaSV;
        document.getElementById('tenSV').value = studentData.TenSV;
        document.getElementById('email').value = studentData.Email;
        document.getElementById('soDT').value = studentData.SoDT;
        document.getElementById('lop').value = studentData.Lop;
        
        // Disable MaSV field (primary key shouldn't be changed)
        document.getElementById('maSV').disabled = true;
        
        // Change form button text
        const submitBtn = document.querySelector('#addStudentForm button[type="submit"]');
        submitBtn.textContent = '💾 Cập Nhật Sinh Viên';
        
        // Set editing mode
        editingStudent = maSV;
        
        // Scroll to form
        document.querySelector('.section').scrollIntoView({ behavior: 'smooth' });
        
        showAlert('📝 Đang chỉnh sửa thông tin sinh viên. Thay đổi thông tin và nhấn "Cập Nhật Sinh Viên"', 'success');
    }
}

// Update student
async function updateStudent() {
    const formData = new FormData(document.getElementById('addStudentForm'));
    const studentData = {
        TenSV: formData.get('tenSV'),
        Email: formData.get('email'),
        SoDT: formData.get('soDT'),
        Lop: formData.get('lop') || null
    };

    // Validate required fields
    if (!studentData.TenSV || !studentData.Email || !studentData.SoDT) {
        showAlert('Vui lòng điền đầy đủ các trường bắt buộc!', 'error');
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(studentData.Email)) {
        showAlert('Định dạng email không hợp lệ!', 'error');
        return;
    }

    // Validate phone number
    const phoneRegex = /^[0-9]{10,11}$/;
    if (!phoneRegex.test(studentData.SoDT)) {
        showAlert('Số điện thoại phải có 10-11 chữ số!', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/SinhVien/${editingStudent}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(studentData)
        });

        if (response.ok) {
            showAlert('✅ Cập nhật thông tin sinh viên thành công!', 'success');
            resetForm();
            loadAllStudents();
        } else {
            const error = await response.json();
            showAlert(`❌ Lỗi: ${error.message || 'Không thể cập nhật sinh viên'}`, 'error');
        }
    } catch (error) {
        showAlert(`❌ Lỗi kết nối: ${error.message}`, 'error');
    }
}

// Delete student
async function deleteStudent(maSV) {
    if (!confirm(`⚠️ Bạn có chắc chắn muốn xóa sinh viên ${maSV}?\n\nHành động này không thể hoàn tác!`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/SinhVien/${maSV}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showAlert('✅ Xóa sinh viên thành công!', 'success');
            loadAllStudents();
        } else {
            const error = await response.json();
            showAlert(`❌ Lỗi: ${error.message || 'Không thể xóa sinh viên'}`, 'error');
        }
    } catch (error) {
        showAlert(`❌ Lỗi kết nối: ${error.message}`, 'error');
    }
}

// Reset form
function resetForm() {
    document.getElementById('addStudentForm').reset();
    document.getElementById('maSV').disabled = false;
    const submitBtn = document.querySelector('#addStudentForm button[type="submit"]');
    submitBtn.textContent = '➕ Thêm Sinh Viên';
    editingStudent = null;
}

// Utility functions
function formatPhoneNumber(phone) {
    // Format phone number for display
    if (phone.length === 10) {
        return phone.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
    } else if (phone.length === 11) {
        return phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
    }
    return phone;
}

function validateStudentData(data) {
    const errors = [];
    
    if (!data.MaSV || data.MaSV.trim() === '') {
        errors.push('Mã sinh viên không được để trống');
    }
    
    if (!data.TenSV || data.TenSV.trim() === '') {
        errors.push('Tên sinh viên không được để trống');
    }
    
    if (!data.Email || data.Email.trim() === '') {
        errors.push('Email không được để trống');
    } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.Email)) {
            errors.push('Định dạng email không hợp lệ');
        }
    }
    
    if (!data.SoDT || data.SoDT.trim() === '') {
        errors.push('Số điện thoại không được để trống');
    } else {
        const phoneRegex = /^[0-9]{10,11}$/;
        if (!phoneRegex.test(data.SoDT)) {
            errors.push('Số điện thoại phải có 10-11 chữ số');
        }
    }
    
    return errors;
}
