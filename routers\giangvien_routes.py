from flask import Blueprint, jsonify, request 
from controllers.giangvien_controller import (
    get_all, create_new, update_information, delete_GV, search_MaGV
)

giangVienBPrint = Blueprint("GiangVien", __name__) 

@giangVienBPrint.route("/GiangVien", methods = ["GET"])
def get_all(): 
    gv_list = get_all()
    return jsonify(gv_list)

@giangVienBPrint.route("/GiangVien", methods = ["POST"])
def create_new_GV(): 
    gv = create_new()
    return jsonify(gv)

@giangVienBPrint.route("/GiangVien/<MaGV>", methods = ["PUT"])
def update_GV(MaGV, data):
    gv = update_information(MaGV= MaGV, data= data) 
    return jsonify(gv)

@giangVienBPrint.route("/GiangVien/<MaGV>", methods = ["DELETE"])
def delete_GV (MaGV): 
    check = delete_GV(MaGV= MaGV)
    if check: 
        return jsonify({"message": "Đã xóa thành công giáo viên"})
    return jsonify({"message": "Không tìm thấy giáo viên"}), 404
@giangVienBPrint.route("/GiangVien/Search", methods = ["GET"])
def search_GV(MaGV): 
    gv_list = search_MaGV(MaGV= MaGV)
    return jsonify(gv_list) 