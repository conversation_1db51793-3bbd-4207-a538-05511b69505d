from flask import Blueprint, jsonify, request
from controllers.sinhvien_controller import (
    get_all_SV, create_new_SV, update_information_SV, delete_SV, search_SV_MaSV, validate_student_data
)

sinhVienBPrint = Blueprint("SinhVien", __name__)

@sinhVienBPrint.route("/SinhVien", methods=["GET"])
def get_all_sinhvien():
    """
    <PERSON><PERSON>y danh sách tất cả sinh viên
    Returns: JSON array chứa thông tin tất cả sinh viên
    """
    try:
        sv_list = get_all_SV()
        return jsonify(sv_list), 200
    except Exception as e:
        return jsonify({"error": f"Lỗi khi lấy danh sách sinh viên: {str(e)}"}), 500

@sinhVienBPrint.route("/SinhVien", methods=["POST"])
def create_sinhvien():
    """
    Tạo sinh viên mới
    Expects: JSON body với <PERSON>, TenSV, <PERSON>ail, SoDT, Lop và tùy chọn MaXL
    Returns: JSON object của sinh viên vừa tạo
    """
    try:
        data = request.get_json()  # Lấy JSON từ request body
        if not data:
            return jsonify({"error": "Không có dữ liệu được gửi"}), 400

        # Validate dữ liệu đầu vào
        validation_errors = validate_student_data(data, is_update=False)
        if validation_errors:
            return jsonify({"errors": validation_errors}), 400

        newSV = create_new_SV(data)
        return jsonify(newSV), 201
    except Exception as e:
        return jsonify({"error": f"Lỗi khi tạo sinh viên: {str(e)}"}), 500

@sinhVienBPrint.route("/SinhVien/<MaSV>", methods=["PUT"])
def update_sinhvien(MaSV):
    """
    Cập nhật thông tin sinh viên
    Args: MaSV - Mã sinh viên cần cập nhật
    Expects: JSON body với dữ liệu cần cập nhật
    Returns: JSON object của sinh viên đã cập nhật
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Không có dữ liệu được gửi"}), 400

        # Validate dữ liệu đầu vào (cho update)
        validation_errors = validate_student_data(data, is_update=True)
        if validation_errors:
            return jsonify({"errors": validation_errors}), 400

        sv = update_information_SV(MaSV=MaSV, data=data)
        if sv:
            return jsonify(sv), 200
        return jsonify({"error": "Không tìm thấy sinh viên"}), 404
    except Exception as e:
        return jsonify({"error": f"Lỗi khi cập nhật sinh viên: {str(e)}"}), 500

@sinhVienBPrint.route("/SinhVien/<MaSV>", methods=["DELETE"])
def delete_sinhvien(MaSV):
    """
    Xóa sinh viên
    Args: MaSV - Mã sinh viên cần xóa
    Returns: JSON message xác nhận xóa thành công hoặc lỗi
    """
    try:
        sv = delete_SV(MaSV=MaSV)
        if sv:
            return jsonify({
                "message": "Đã xóa thành công sinh viên",
                "deleted_student": sv
            }), 200
        return jsonify({"error": "Không tìm thấy sinh viên"}), 404
    except Exception as e:
        return jsonify({"error": f"Lỗi khi xóa sinh viên: {str(e)}"}), 500

@sinhVienBPrint.route("/SinhVien/Search", methods=["GET"])
def search_sinhvien():
    """
    Tìm kiếm sinh viên theo mã
    Query params: MaSV - Mã sinh viên cần tìm (có thể là một phần)
    Returns: JSON array chứa các sinh viên phù hợp
    """
    try:
        MaSV = request.args.get("MaSV")
        if not MaSV:
            return jsonify({"error": "Tham số MaSV là bắt buộc"}), 400

        sv_list = search_SV_MaSV(MaSV=MaSV)
        return jsonify(sv_list), 200
    except Exception as e:
        return jsonify({"error": f"Lỗi khi tìm kiếm sinh viên: {str(e)}"}), 500
