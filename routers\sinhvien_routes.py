from flask import Blueprint , jsonify, request
from controllers.sinhvien_controller import (
    get_all_SV, create_new_SV, update_information_SV, delete_SV, search_SV_MaSV, serialize_SV
)

sinhVienBPrint = Blueprint("SinhVien", __name__)

@sinhVienBPrint.route("/SinhVien", methods = ["GET"])
def get_all(): 
    sv_list = get_all_SV()
    result = []
    for sv in  sv_list: 
        result.append(serialize_SV(sv))
    return jsonify(result)

@sinhVienBPrint.route("/SinhVien", methods = ["POST"] )
def create_sv() : 
    data = request.get_json() # Lấy JSON từ request body
    newSV = create_new_SV(data) 
    return jsonify(serialize_SV(newSV)), 201 

@sinhVienBPrint.route("/SinhVien/<MaSV>", methods = ["PUT"])
def update_sv(MaSV): 
    data = request.get_json()
    sv = update_information_SV(MaSV= MaSV, data= data)
    if sv: 
        return jsonify(serialize_SV(sv= sv))
    return jsonify({"message": "Không tìm thấy sinh viên"}), 404

@sinhVienBPrint.route("/SinhVien/<MaSV>", methods = ["DELETE"])
def delete_sv(MaSV): 
    sv = delete_SV(MaSV= MaSV)
    if sv: 
        return jsonify({"message": "Đã xóa thành công sinh viên"})
    return jsonify({"message": "Không tìm thấy sinh viên"}), 404

@sinhVienBPrint.route("/SinhVien/Search", methods=["GET"]) 
def search_sv():
    MaSV = request.args.get("MaSV")
    if not MaSV:
        return jsonify({"message": "MaSV parameter is required"}), 400
    sv_list = search_SV_MaSV(MaSV=MaSV)
    result = []
    for sv in sv_list:
        result.append(serialize_SV(sv))
    return jsonify(result)
    