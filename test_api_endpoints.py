#!/usr/bin/env python3
"""
Comprehensive API Testing Script
Tests all CRUD operations for both SinhVien and GiangVien endpoints
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5000/api"
HEADERS = {"Content-Type": "application/json"}

class APITester:
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
    
    def log_test(self, test_name, passed, message="", response_data=None):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} - {test_name}")
        if message:
            print(f"    {message}")
        if response_data and not passed:
            print(f"    Response: {response_data}")
        
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        print()
    
    def test_get_request(self, endpoint, expected_status=200, test_name=""):
        """Test GET request"""
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            passed = response.status_code == expected_status
            
            if passed:
                data = response.json() if response.content else []
                message = f"Status: {response.status_code}, Items: {len(data) if isinstance(data, list) else 'N/A'}"
            else:
                message = f"Expected status {expected_status}, got {response.status_code}"
            
            self.log_test(test_name or f"GET {endpoint}", passed, message, 
                         response.json() if not passed and response.content else None)
            return response
        except Exception as e:
            self.log_test(test_name or f"GET {endpoint}", False, f"Exception: {str(e)}")
            return None
    
    def test_post_request(self, endpoint, data, expected_status=201, test_name=""):
        """Test POST request"""
        try:
            response = requests.post(f"{BASE_URL}{endpoint}", json=data, headers=HEADERS)
            passed = response.status_code == expected_status
            
            if passed:
                message = f"Status: {response.status_code}, Created successfully"
            else:
                message = f"Expected status {expected_status}, got {response.status_code}"
            
            self.log_test(test_name or f"POST {endpoint}", passed, message,
                         response.json() if not passed and response.content else None)
            return response
        except Exception as e:
            self.log_test(test_name or f"POST {endpoint}", False, f"Exception: {str(e)}")
            return None
    
    def test_put_request(self, endpoint, data, expected_status=200, test_name=""):
        """Test PUT request"""
        try:
            response = requests.put(f"{BASE_URL}{endpoint}", json=data, headers=HEADERS)
            passed = response.status_code == expected_status
            
            if passed:
                message = f"Status: {response.status_code}, Updated successfully"
            else:
                message = f"Expected status {expected_status}, got {response.status_code}"
            
            self.log_test(test_name or f"PUT {endpoint}", passed, message,
                         response.json() if not passed and response.content else None)
            return response
        except Exception as e:
            self.log_test(test_name or f"PUT {endpoint}", False, f"Exception: {str(e)}")
            return None
    
    def test_delete_request(self, endpoint, expected_status=200, test_name=""):
        """Test DELETE request"""
        try:
            response = requests.delete(f"{BASE_URL}{endpoint}")
            passed = response.status_code == expected_status
            
            if passed:
                message = f"Status: {response.status_code}, Deleted successfully"
            else:
                message = f"Expected status {expected_status}, got {response.status_code}"
            
            self.log_test(test_name or f"DELETE {endpoint}", passed, message,
                         response.json() if not passed and response.content else None)
            return response
        except Exception as e:
            self.log_test(test_name or f"DELETE {endpoint}", False, f"Exception: {str(e)}")
            return None

def test_sinhvien_endpoints(tester):
    """Test all SinhVien endpoints"""
    print("🎓 TESTING SINHVIEN ENDPOINTS")
    print("=" * 50)
    
    # Test GET all students
    tester.test_get_request("/SinhVien", test_name="Get all SinhVien")
    
    # Test POST - Create new student (using unique data)
    import random
    unique_id = random.randint(1000, 9999)
    test_student = {
        "MaSV": f"TST{unique_id}",
        "TenSV": f"Test Student {unique_id}",
        "Email": f"test{unique_id}@example.com",
        "SoDT": f"09{unique_id}1234",  # Unique 10-digit phone number
        "Lop": "TEST_CLASS"
    }
    
    create_response = tester.test_post_request("/SinhVien", test_student, 
                                              test_name="Create new SinhVien")
    
    if create_response and create_response.status_code == 201:
        # Test duplicate creation BEFORE deleting the student
        tester.test_post_request("/SinhVien", test_student, expected_status=500,
                                test_name="Duplicate MaSV handling")

        # Test GET specific student (search)
        tester.test_get_request(f"/SinhVien/Search?MaSV={test_student['MaSV']}",
                               test_name="Search SinhVien by MaSV")

        # Test PUT - Update student
        update_data = {
            "TenSV": "Updated Test Student 001",
            "Email": "<EMAIL>"
        }
        tester.test_put_request(f"/SinhVien/{test_student['MaSV']}", update_data,
                               test_name="Update SinhVien")

        # Test DELETE student
        tester.test_delete_request(f"/SinhVien/{test_student['MaSV']}",
                                  test_name="Delete SinhVien")

    # Test validation errors
    invalid_student = {
        "MaSV": "",
        "TenSV": "",
        "Email": "invalid-email",
        "SoDT": "123",
        "Lop": ""
    }
    tester.test_post_request("/SinhVien", invalid_student, expected_status=400,
                            test_name="Validation error handling")

def test_giangvien_endpoints(tester):
    """Test all GiangVien endpoints"""
    print("👨‍🏫 TESTING GIANGVIEN ENDPOINTS")
    print("=" * 50)
    
    # Test GET all teachers
    tester.test_get_request("/GiangVien", test_name="Get all GiangVien")
    
    # Test POST - Create new teacher (using shorter unique ID)
    import random
    unique_id = random.randint(100, 999)  # 3 digits to fit in 10-char limit
    test_teacher = {
        "MaGV": f"TGV{unique_id}",  # 6 characters total
        "TenGV": f"Test Teacher {unique_id}"
    }
    
    create_response = tester.test_post_request("/GiangVien", test_teacher,
                                              test_name="Create new GiangVien")
    
    if create_response and create_response.status_code == 201:
        # Test search
        tester.test_get_request(f"/GiangVien/Search?MaGV={test_teacher['MaGV']}",
                               test_name="Search GiangVien by MaGV")

        # Test PUT - Update teacher
        update_data = {
            "TenGV": f"Updated {test_teacher['TenGV']}"
        }
        tester.test_put_request(f"/GiangVien/{test_teacher['MaGV']}", update_data,
                               test_name="Update GiangVien")

        # Test DELETE teacher
        tester.test_delete_request(f"/GiangVien/{test_teacher['MaGV']}",
                                  test_name="Delete GiangVien")
    
    # Test validation errors
    invalid_teacher = {
        "MaGV": "",
        "TenGV": ""
    }
    tester.test_post_request("/GiangVien", invalid_teacher, expected_status=400,
                            test_name="GiangVien validation error handling")

def main():
    """Main testing function"""
    print("🚀 STARTING COMPREHENSIVE API TESTING")
    print("=" * 60)
    print(f"Testing against: {BASE_URL}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tester = APITester()
    
    try:
        # Test server connectivity
        response = requests.get(f"{BASE_URL.replace('/api', '')}/")
        if response.status_code != 200:
            print("❌ Server is not running or not accessible!")
            sys.exit(1)
        
        # Run tests
        test_sinhvien_endpoints(tester)
        test_giangvien_endpoints(tester)
        
        # Print summary
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"✅ Passed: {tester.passed_tests}")
        print(f"❌ Failed: {tester.failed_tests}")
        print(f"📈 Success Rate: {(tester.passed_tests / (tester.passed_tests + tester.failed_tests) * 100):.1f}%")
        
        if tester.failed_tests > 0:
            print("\n🔍 FAILED TESTS:")
            for result in tester.test_results:
                if not result['passed']:
                    print(f"  - {result['test']}: {result['message']}")
        
        return tester.failed_tests == 0
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Flask app is running on http://127.0.0.1:5000")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
