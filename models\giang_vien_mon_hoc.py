from config import db

class GiangVien_MonHoc(db.Model):
    __tablename__ = "GiangVien_MonHoc"
    MaGV = db.<PERSON>umn(db.String(10), db.<PERSON><PERSON><PERSON>("GiangVien.MaGV", onupdate="CASCADE", ondelete="CASCADE"), primary_key=True)
    MaMH = db.<PERSON>umn(db.String(10), db.<PERSON><PERSON>("MonHoc.MaMH", onupdate="CASCADE", ondelete="CASCADE"), primary_key=True)  # Fixed: ondelte -> ondelete
    HocKy = db.<PERSON>umn(db.Integer, primary_key=True)  
    NamHoc = db.Column(db.Integer, primary_key=True)

    