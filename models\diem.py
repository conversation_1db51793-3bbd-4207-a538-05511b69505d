from config import db

class Diem(db.Model):
    __tablename__ = "Diem"

    MaSV = db.<PERSON><PERSON><PERSON>(db.String(20), db.<PERSON><PERSON><PERSON>("SinhVien.MaSV", onupdate="CASCADE", ondelete="CASCADE"), primary_key=True)
    MaMH = db.<PERSON>umn(db.String(10), db.<PERSON><PERSON>("MonHoc.MaMH", onupdate="CASCADE", ondelete="CASCADE"), primary_key=True)
    HocKy = db.Column(db.Integer, primary_key=True)
    NamHoc = db.Column(db.Integer, primary_key=True)
    Diem = db.Column(db.Numeric(4,2))  
