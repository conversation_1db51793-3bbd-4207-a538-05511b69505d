# SinhVien Controller - Raw SQL Implementation
# Chuyển đổi từ ORM (SQLAlchemy) sang Raw SQL để có hiệu suất tốt hơn và kiểm soát tốt hơn

from config import get_db_connection

def get_all_SV():
    """
    L<PERSON>y danh sách tất cả sinh viên từ database sử dụng Raw SQL

    Returns:
        list: Danh sách các dictionary chứa thông tin sinh viên
              Mỗi dictionary có format: {MaSV, TenSV, Email, SoDT, Lop, MaXL}

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Tạo câu lệnh SQL để lấy tất cả sinh viên, sắp xếp theo MaSV
            sql = """
                SELECT MaSV, TenSV, Email, SoDT, Lop, MaXL
                FROM SinhVien
                ORDER BY MaSV
            """
            cursor.execute(sql)

            # Chuyển đổi kết quả thành list of dictionaries để dễ sử dụng
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results
    except Exception as e:
        raise Exception(f"Lỗi khi lấy danh sách sinh viên: {str(e)}")

def create_new_SV(data):
    """
    Tạo sinh viên mới trong database sử dụng Raw SQL

    Args:
        data (dict): Dictionary chứa thông tin sinh viên
                    Bắt buộc có: MaSV, TenSV, Email, SoDT, Lop
                    Tùy chọn: MaXL

    Returns:
        dict: Thông tin sinh viên vừa được tạo

    Raises:
        Exception: Khi có lỗi kết nối database, thực thi SQL, hoặc dữ liệu không hợp lệ
    """
    try:
        # Kiểm tra dữ liệu đầu vào
        required_fields = ['MaSV', 'TenSV', 'Email', 'SoDT', 'Lop']
        missing_fields = [field for field in required_fields if not data.get(field)]

        if missing_fields:
            raise ValueError(f"Thiếu các trường bắt buộc: {', '.join(missing_fields)}")

        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem MaSV đã tồn tại chưa
            check_sql = "SELECT COUNT(*) FROM SinhVien WHERE MaSV = ?"
            cursor.execute(check_sql, (data["MaSV"],))
            if cursor.fetchone()[0] > 0:
                raise ValueError(f"Mã sinh viên {data['MaSV']} đã tồn tại")

            # Kiểm tra email đã tồn tại chưa (email phải unique)
            email_check_sql = "SELECT COUNT(*) FROM SinhVien WHERE Email = ?"
            cursor.execute(email_check_sql, (data["Email"],))
            if cursor.fetchone()[0] > 0:
                raise ValueError(f"Email {data['Email']} đã được sử dụng")

            # Kiểm tra số điện thoại đã tồn tại chưa (SoDT phải unique)
            phone_check_sql = "SELECT COUNT(*) FROM SinhVien WHERE SoDT = ?"
            cursor.execute(phone_check_sql, (data["SoDT"],))
            if cursor.fetchone()[0] > 0:
                raise ValueError(f"Số điện thoại {data['SoDT']} đã được sử dụng")

            # Thêm sinh viên mới
            insert_sql = """
                INSERT INTO SinhVien (MaSV, TenSV, Email, SoDT, Lop, MaXL)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            cursor.execute(insert_sql, (
                data["MaSV"],
                data["TenSV"],
                data["Email"],
                data["SoDT"],
                data["Lop"],
                data.get("MaXL")  # MaXL có thể null
            ))
            conn.commit()

            # Trả về thông tin sinh viên vừa tạo
            return {
                "MaSV": data["MaSV"],
                "TenSV": data["TenSV"],
                "Email": data["Email"],
                "SoDT": data["SoDT"],
                "Lop": data["Lop"],
                "MaXL": data.get("MaXL")
            }
    except ValueError as ve:
        raise Exception(str(ve))
    except Exception as e:
        raise Exception(f"Lỗi khi tạo sinh viên: {str(e)}")

def update_information_SV(MaSV, data):
    """
    Cập nhật thông tin sinh viên sử dụng Raw SQL

    Args:
        MaSV (str): Mã sinh viên cần cập nhật
        data (dict): Dictionary chứa thông tin cần cập nhật

    Returns:
        dict: Thông tin sinh viên sau khi cập nhật, hoặc None nếu không tìm thấy

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem sinh viên có tồn tại không và lấy thông tin hiện tại
            check_sql = """
                SELECT MaSV, TenSV, Email, SoDT, Lop, MaXL
                FROM SinhVien
                WHERE MaSV = ?
            """
            cursor.execute(check_sql, (MaSV,))
            current_data = cursor.fetchone()

            if not current_data:
                return None  # Không tìm thấy sinh viên

            # Chuyển đổi current_data thành dictionary
            columns = [column[0] for column in cursor.description]
            current_sv = dict(zip(columns, current_data))

            # Chuẩn bị dữ liệu cập nhật (giữ nguyên giá trị cũ nếu không có giá trị mới)
            updated_data = {
                "TenSV": data.get("TenSV", current_sv["TenSV"]),
                "Email": data.get("Email", current_sv["Email"]),
                "SoDT": data.get("SoDT", current_sv["SoDT"]),
                "Lop": data.get("Lop", current_sv["Lop"]),
                "MaXL": data.get("MaXL", current_sv["MaXL"])
            }

            # Kiểm tra email unique (nếu email thay đổi)
            if updated_data["Email"] != current_sv["Email"]:
                email_check_sql = "SELECT COUNT(*) FROM SinhVien WHERE Email = ? AND MaSV != ?"
                cursor.execute(email_check_sql, (updated_data["Email"], MaSV))
                if cursor.fetchone()[0] > 0:
                    raise ValueError(f"Email {updated_data['Email']} đã được sử dụng")

            # Kiểm tra số điện thoại unique (nếu SoDT thay đổi)
            if updated_data["SoDT"] != current_sv["SoDT"]:
                phone_check_sql = "SELECT COUNT(*) FROM SinhVien WHERE SoDT = ? AND MaSV != ?"
                cursor.execute(phone_check_sql, (updated_data["SoDT"], MaSV))
                if cursor.fetchone()[0] > 0:
                    raise ValueError(f"Số điện thoại {updated_data['SoDT']} đã được sử dụng")

            # Cập nhật thông tin sinh viên
            update_sql = """
                UPDATE SinhVien
                SET TenSV = ?, Email = ?, SoDT = ?, Lop = ?, MaXL = ?
                WHERE MaSV = ?
            """
            cursor.execute(update_sql, (
                updated_data["TenSV"],
                updated_data["Email"],
                updated_data["SoDT"],
                updated_data["Lop"],
                updated_data["MaXL"],
                MaSV
            ))
            conn.commit()

            # Trả về thông tin đã cập nhật
            return {
                "MaSV": MaSV,
                "TenSV": updated_data["TenSV"],
                "Email": updated_data["Email"],
                "SoDT": updated_data["SoDT"],
                "Lop": updated_data["Lop"],
                "MaXL": updated_data["MaXL"]
            }
    except ValueError as ve:
        raise Exception(str(ve))
    except Exception as e:
        raise Exception(f"Lỗi khi cập nhật sinh viên: {str(e)}")

def delete_SV(MaSV):
    """
    Xóa sinh viên khỏi database sử dụng Raw SQL

    Args:
        MaSV (str): Mã sinh viên cần xóa

    Returns:
        dict: Thông tin sinh viên đã xóa, hoặc None nếu không tìm thấy

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Kiểm tra xem sinh viên có tồn tại không và lấy thông tin trước khi xóa
            check_sql = """
                SELECT MaSV, TenSV, Email, SoDT, Lop, MaXL
                FROM SinhVien
                WHERE MaSV = ?
            """
            cursor.execute(check_sql, (MaSV,))
            student_data = cursor.fetchone()

            if not student_data:
                return None  # Không tìm thấy sinh viên

            # Chuyển đổi thành dictionary để trả về
            columns = [column[0] for column in cursor.description]
            student_info = dict(zip(columns, student_data))

            # Xóa sinh viên
            delete_sql = "DELETE FROM SinhVien WHERE MaSV = ?"
            cursor.execute(delete_sql, (MaSV,))
            conn.commit()

            return student_info
    except Exception as e:
        raise Exception(f"Lỗi khi xóa sinh viên: {str(e)}")

def search_SV_MaSV(MaSV):
    """
    Tìm kiếm sinh viên theo mã (hỗ trợ tìm kiếm một phần) sử dụng Raw SQL

    Args:
        MaSV (str): Mã sinh viên cần tìm (có thể là một phần)

    Returns:
        list: Danh sách các dictionary chứa thông tin sinh viên phù hợp

    Raises:
        Exception: Khi có lỗi kết nối database hoặc thực thi SQL
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Sử dụng parameterized query để tránh SQL injection
            # FIXED: Sửa lỗi SQL injection bằng cách sử dụng parameter thay vì string formatting
            sql = """
                SELECT MaSV, TenSV, Email, SoDT, Lop, MaXL
                FROM SinhVien
                WHERE MaSV LIKE ?
                ORDER BY MaSV
            """
            search_pattern = f"%{MaSV}%"  # Tạo pattern tìm kiếm
            cursor.execute(sql, (search_pattern,))

            # Chuyển đổi kết quả thành list of dictionaries
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))

            return results
    except Exception as e:
        raise Exception(f"Lỗi khi tìm kiếm sinh viên: {str(e)}")

def serialize_SV(sv):
    """
    Chuyển đổi object sinh viên thành dictionary (để tương thích với code cũ)

    Args:
        sv: Có thể là dictionary (từ Raw SQL) hoặc SQLAlchemy object

    Returns:
        dict: Dictionary chứa thông tin sinh viên

    Note: Function này giữ lại để tương thích với routes hiện tại
          Trong Raw SQL implementation, dữ liệu đã là dictionary rồi
    """
    # Nếu sv đã là dictionary (từ Raw SQL), trả về luôn
    if isinstance(sv, dict):
        return sv

    # Nếu sv là SQLAlchemy object (để tương thích với code cũ)
    return {
        "MaSV": sv.MaSV,
        "TenSV": sv.TenSV,
        "Email": sv.Email,
        "SoDT": sv.SoDT,
        "Lop": sv.Lop,
        "MaXL": sv.MaXL
    }

# Thêm function helper để validate dữ liệu sinh viên
def validate_student_data(data, is_update=False):
    """
    Validate dữ liệu sinh viên

    Args:
        data (dict): Dữ liệu cần validate
        is_update (bool): True nếu đây là update operation

    Returns:
        list: Danh sách các lỗi validation (empty list nếu không có lỗi)
    """
    errors = []

    # Kiểm tra các trường bắt buộc (chỉ khi tạo mới)
    if not is_update:
        required_fields = ['MaSV', 'TenSV', 'Email', 'SoDT', 'Lop']
        for field in required_fields:
            if not data.get(field):
                errors.append(f"Trường {field} là bắt buộc")

    # Validate email format
    email = data.get('Email')
    if email and '@' not in email:
        errors.append("Email không hợp lệ")

    # Validate số điện thoại (10-11 chữ số)
    phone = data.get('SoDT')
    if phone and (not phone.isdigit() or len(phone) < 10 or len(phone) > 11):
        errors.append("Số điện thoại phải có 10-11 chữ số")

    # Validate MaSV format (nếu có)
    ma_sv = data.get('MaSV')
    if ma_sv and len(ma_sv.strip()) == 0:
        errors.append("Mã sinh viên không được để trống")

    return errors
