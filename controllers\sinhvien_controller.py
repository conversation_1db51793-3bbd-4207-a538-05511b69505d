from models.sinh_vien import <PERSON>h<PERSON><PERSON>
from config import db 

def get_all_SV(): 
    sv_list = SinhVien.query.all()
    return sv_list

def create_new_SV(data):
    new_sv = SinhVien(
        MaSV = data["MaSV"], 
        TenSV = data["TenSV"], 
        Email = data["Email"], 
        SoDT = data["SoDT"], 
        Lop = data["Lop"], 
        MaXL = data.get("MaXL") 
    )
    db.session.add(new_sv)
    db.session.commit()
    return new_sv

def update_information_SV(MaSV, data): 
    sv = SinhVien.query.get(MaSV)
    if sv: 
        sv.TenSV = data.get("TenSV", sv.TenSV)
        sv.Email = data.get("Email", sv.Email)
        sv.SoDT = data.get("SoDT", sv.SoDT)
        sv.Lop = data.get("Lop", sv.Lop)
        sv.MaXL = data.get("MaXL", sv.MaXL)
        db.session.commit()
        return sv
    return None 

def delete_SV(MaSV): 
    sv = SinhVien.query.get(MaSV)
    if sv: 
        db.session.delete(sv)
        db.session.commit()
        return sv
    return None 

def search_SV_MaSV(MaSV): 
    # WHERE MaSV LIKE '%keyword%'
    sv_list = SinhVien.query.filter(SinhVien.MaSV.like(f"%{MaSV}%")).all()
    return sv_list 



def serialize_SV(sv): 
    return {
        "MaSV": sv.MaSV, 
        "TenSV": sv.TenSV, 
        "Email": sv.Email, 
        "SoDT": sv.SoDT, 
        "Lop": sv.Lop, 
        "MaXL": sv.MaXL
    }
