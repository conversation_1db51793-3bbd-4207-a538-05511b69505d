from config import db

class SinhVien(db.Model):
    __tablename__ = "SinhVien"

    MaSV = db.Column(db.String(20), primary_key=True)
    TenSV = db.Column(db.String(100), nullable=False) 
    Email = db.Column(db.String(255), nullable=False, unique=True)
    SoDT = db.Column(db.String(15), nullable=False, unique=True)
    Lop = db.Column(db.String(50))
    MaXL = db.Column(db.String(5), db.<PERSON><PERSON>("XepLoai.MaXL", onupdate="CASCADE", ondelete="SET NULL"))

    