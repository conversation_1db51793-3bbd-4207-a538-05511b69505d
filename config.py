import os 
import urllib.parse
from flask_sqlalchemy import SQLAlchemy
from dotenv import load_dotenv
import pyodbc

load_dotenv() # đọc file .env

DB_SERVER=os.getenv("DB_SERVER")
DB_NAME=os.getenv("DB_NAME")
DB_USER=os.getenv("DB_USER")
DB_PASSWORD=os.getenv("DB_PASSWORD")
DB_DRIVER=os.getenv("DB_DRIVER")
DB_USE_WINDOWS_AUTH=os.getenv("DB_USE_WINDOWS_AUTH", "false").lower() == "true"
DB_CONNECTION_TIMEOUT=os.getenv("DB_CONNECTION_TIMEOUT", "30")
DB_COMMAND_TIMEOUT=os.getenv("DB_COMMAND_TIMEOUT", "30")

# Tạo chuỗi kết nối ODBC cho SQLAlchemy
# Build connection string based on authentication method
if DB_USE_WINDOWS_AUTH:
    # Windows Authentication
    connection_string = (
        f"DRIVER={{{DB_DRIVER}}};"
        f"SERVER={DB_SERVER};"
        f"DATABASE={DB_NAME};"
        "Trusted_Connection=yes;"
        f"Connection Timeout={DB_CONNECTION_TIMEOUT};"
        f"Command Timeout={DB_COMMAND_TIMEOUT};"
        "TrustServerCertificate=yes;"
    )
else:
    # SQL Server Authentication
    connection_string = (
        f"DRIVER={{{DB_DRIVER}}};"
        f"SERVER={DB_SERVER};"
        f"DATABASE={DB_NAME};"
        f"UID={DB_USER};"
        f"PWD={DB_PASSWORD};"
        f"Connection Timeout={DB_CONNECTION_TIMEOUT};"
        f"Command Timeout={DB_COMMAND_TIMEOUT};"
        "TrustServerCertificate=yes;"
    )

params = urllib.parse.quote_plus(connection_string)

SQLALCHEMY_DATABASE_URI = f"mssql+pyodbc:///?odbc_connect={params}"

# Tạo đối tượng db để các model import dùng
db = SQLAlchemy() 


# RAW SQL 
def get_db_connection():
    conn = pyodbc.connect(
        "DRIVER={ODBC Driver 17 for SQL Server};"
        f"SERVER={DB_SERVER};"
        f"DATABASE={DB_NAME};"
        f"UID={DB_USER};"
        f"PWD={DB_PASSWORD};"
        "TrustServerCertificate=yes;"
    )
    return conn




